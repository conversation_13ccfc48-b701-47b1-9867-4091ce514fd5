package com.amobear.themepack.presentation.rewards

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.amobear.themepack.presentation.main.navigation.destination.ThemeNavigationDestination

data object RewardsDestination : ThemeNavigationDestination {
    override val route: String = "rewards_route"
    override val destination: String = "rewards_destination"
}

fun NavGraphBuilder.rewardsGraph(
    onNavigateBack: () -> Unit,
) {
    composable(route = RewardsDestination.route) {
        RewardsRoute(onNavigateBack = onNavigateBack)
    }
}

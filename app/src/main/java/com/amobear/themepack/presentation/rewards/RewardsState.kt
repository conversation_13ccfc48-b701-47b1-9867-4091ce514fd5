package com.amobear.themepack.presentation.rewards

import com.amobear.themepack.data.model.CoinPackage
import com.amobear.themepack.data.model.DailyReward
import com.amobear.themepack.data.model.RewardTask
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

data class RewardsState(
    val isLoading: Boolean = false,
    val coinBalance: Int = 0,
    val dailyRewards: ImmutableList<DailyReward> = persistentListOf(),
    val currentStreak: Int = 0,
    val rewardTasks: ImmutableList<RewardTask> = persistentListOf(),
    val coinPackages: ImmutableList<CoinPackage> = persistentListOf(),
    val remindMeTomorrow: Boolean = false,
    val showCheckInInfoDialog: Boolean = false,
    val error: String? = null
) {
    companion object {
        val initial = RewardsState()
    }
}

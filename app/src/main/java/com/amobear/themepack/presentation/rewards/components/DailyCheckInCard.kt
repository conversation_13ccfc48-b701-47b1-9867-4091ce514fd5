package com.amobear.themepack.presentation.rewards.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.amobear.themepack.R
import com.amobear.themepack.data.model.DailyReward
import kotlinx.collections.immutable.persistentListOf

@Composable
fun DailyCheckInCard(
    dailyRewards: List<DailyReward>,
    currentStreak: Int,
    onCheckInClick: () -> Unit,
    showInfoDialog: Boolean = false,
    onShowInfoDialog: () -> Unit = {},
    onDismissInfoDialog: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Streak text with info button
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.check_in_streak, currentStreak),
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.weight(1f)
                )
                
                // Info button
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .clip(CircleShape)
                        .background(Color.Gray.copy(alpha = 0.2f))
                        .clickable { onShowInfoDialog() },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "?",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Gray
                    )
                }
            }
            
            // Daily rewards row
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                itemsIndexed(dailyRewards) { index, reward ->
                    DailyRewardItem(
                        reward = reward,
                        modifier = Modifier.width(60.dp)
                    )
                }
            }
            
            // Check in button
            Button(
                onClick = onCheckInClick,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF2196F3)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = stringResource(R.string.check_in_button),
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
    
    // Info dialog
    CheckInInfoDialog(
        isVisible = showInfoDialog,
        onDismiss = onDismissInfoDialog
    )
}

@Composable
fun DailyRewardItem(
    reward: DailyReward,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        // Coin amount with background
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(
                    when {
                        reward.isToday -> Color(0xFF2196F3)
                        reward.isCompleted -> Color(0xFFFFD700)
                        else -> Color(0xFFF5F5F5)
                    }
                ),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_coin),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = "+${reward.coins}",
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (reward.isToday || reward.isCompleted) Color.White else Color.Gray
                )
            }
        }
        
        // Day label
        Text(
            text = if (reward.isToday) stringResource(R.string.today_text) else "Day ${reward.day}",
            fontSize = 10.sp,
            color = Color.Gray,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DailyCheckInCardPreview() {
    DailyCheckInCard(
        dailyRewards = listOf(
            DailyReward(1, 10, isCompleted = true),
            DailyReward(2, 20, isCompleted = true),
            DailyReward(3, 30, isToday = true),
            DailyReward(4, 60),
            DailyReward(5, 100),
            DailyReward(6, 200),
            DailyReward(7, 400)
        ),
        currentStreak = 2,
        onCheckInClick = {},
        showInfoDialog = false,
        onShowInfoDialog = {},
        onDismissInfoDialog = {}
    )
}
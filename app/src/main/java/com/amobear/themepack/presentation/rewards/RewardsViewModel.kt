package com.amobear.themepack.presentation.rewards

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.R
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.data.model.CoinPackage
import com.amobear.themepack.data.model.DailyReward
import com.amobear.themepack.data.model.RewardTask
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class RewardsViewModel @Inject constructor(
    private val preferenceManager: SharePreferenceProvider
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(RewardsUiState())
    val uiState: StateFlow<RewardsUiState> = _uiState.asStateFlow()
    
    init {
        loadUserCoins()
        loadRewardsData()
    }

    private fun loadUserCoins() {
        val coins = preferenceManager.getUserCoins()
        _uiState.value = _uiState.value.copy(coinBalance = coins)
    }
    
    private fun loadRewardsData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                dailyRewards = getDailyRewards(),
                rewardTasks = getRewardTasks(),
                coinPackages = getCoinPackages(),
                currentStreak = 2 // Sample streak
            )
        }
    }
    
    private fun getDailyRewards(): List<DailyReward> {
        return listOf(
            DailyReward(1, 10, isCompleted = true),
            DailyReward(2, 20, isCompleted = true),
            DailyReward(3, 30, isToday = true),
            DailyReward(4, 60),
            DailyReward(5, 100),
            DailyReward(6, 200),
            DailyReward(7, 400)
        )
    }
    
    private fun getRewardTasks(): List<RewardTask> {
        return listOf(
            RewardTask(
                id = "daily_gift",
                title = "Daily Gift",
                description = "Get +50 coins per day",
                coins = 50,
                iconRes = R.drawable.ic_coin,
                actionText = "Claim"
            ),
            RewardTask(
                id = "push_notifications",
                title = "On push notifications",
                description = "Get +50 coins when turn on push notifications",
                coins = 50,
                iconRes = R.drawable.ic_coin,
                actionText = "Claim"
            ),
            RewardTask(
                id = "watch_ads",
                title = "Watch Ads (0/7)",
                description = "Get +50 coins by watching ads",
                coins = 50,
                iconRes = R.drawable.ic_coin,
                actionText = "Watch"
            ),
            RewardTask(
                id = "share_friends",
                title = "Share with friends",
                description = "Get +50 coins by sharing the app with your friends",
                coins = 50,
                iconRes = R.drawable.ic_coin,
                actionText = "Share"
            )
        )
    }
    
    private fun getCoinPackages(): List<CoinPackage> {
        return listOf(
            CoinPackage("500", 500, "$1.99", R.drawable.ic_coin),
            CoinPackage("1500", 1500, "$2.99", R.drawable.ic_coin),
            CoinPackage("2500", 2500, "$3.99", R.drawable.ic_coin),
            CoinPackage("3500", 3500, "$4.99", R.drawable.ic_coin),
            CoinPackage("5000", 5000, "$5.99", R.drawable.ic_coin),
            CoinPackage("6000", 6000, "$6.99", R.drawable.ic_coin)
        )
    }
    
    fun onCheckIn() {
        viewModelScope.launch {
            // Handle check-in logic
            preferenceManager.addUserCoins(30)
            val newCoins = preferenceManager.getUserCoins()
            val currentState = _uiState.value
            _uiState.value = currentState.copy(
                coinBalance = newCoins,
                currentStreak = currentState.currentStreak + 1
            )
        }
    }
    
    fun onTaskAction(taskId: String) {
        viewModelScope.launch {
            // Handle task action based on taskId
            when (taskId) {
                "daily_gift" -> claimDailyGift()
                "push_notifications" -> enablePushNotifications()
                "watch_ads" -> watchAds()
                "share_friends" -> shareWithFriends()
            }
        }
    }
    
    fun onPurchaseCoinPackage(packageId: String) {
        viewModelScope.launch {
            // Handle coin package purchase using Google Play Billing
            // TODO: Integrate with Google Play Billing Library
            // TODO: Validate purchase on server
            // TODO: Award coins based on package purchased
            
            // Example logic after successful purchase validation:
            val coinPackage = _uiState.value.coinPackages.find { it.id == packageId }
            coinPackage?.let { pkg ->
                preferenceManager.addUserCoins(pkg.coins)
                val newCoins = preferenceManager.getUserCoins()
                _uiState.value = _uiState.value.copy(coinBalance = newCoins)
            }
        }
    }
    
    fun onRemindMeTomorrowChanged(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(remindMeTomorrow = enabled)
    }
    
    fun onShowCheckInInfoDialog() {
        _uiState.value = _uiState.value.copy(showCheckInInfoDialog = true)
    }
    
    fun onDismissCheckInInfoDialog() {
        _uiState.value = _uiState.value.copy(showCheckInInfoDialog = false)
    }
    
    private fun claimDailyGift() {
        preferenceManager.addUserCoins(50)
        val newCoins = preferenceManager.getUserCoins()
        _uiState.value = _uiState.value.copy(
            coinBalance = newCoins
        )
    }
    
    private fun enablePushNotifications() {
        // Enable push notifications and reward user
        // TODO: Integrate with Firebase Cloud Messaging or other push notification service
        preferenceManager.addUserCoins(50)
        val newCoins = preferenceManager.getUserCoins()
        _uiState.value = _uiState.value.copy(coinBalance = newCoins)
    }
    
    private fun watchAds() {
        // Show rewarded ad and reward user upon completion
        // TODO: Integrate with ad SDK (AdMob, Unity Ads, etc.)
        // On ad completion, call: preferenceManager.addUserCoins(50)
    }
    
    private fun shareWithFriends() {
        // Handle app sharing and reward user
        // TODO: Integrate with sharing functionality
        // TODO: Track successful shares and reward accordingly
        // On successful share, call: preferenceManager.addUserCoins(50)
    }
} 
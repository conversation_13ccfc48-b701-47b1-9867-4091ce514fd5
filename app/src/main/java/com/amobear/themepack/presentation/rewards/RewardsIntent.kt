package com.amobear.themepack.presentation.rewards

sealed class RewardsIntent {
    data object Initialize : RewardsIntent()
    data object CheckIn : RewardsIntent()
    data class TaskAction(val taskId: String) : RewardsIntent()
    data class PurchaseCoinPackage(val packageId: String) : RewardsIntent()
    data class RemindMeTomorrowChanged(val enabled: Boolean) : RewardsIntent()
    data object ShowCheckInInfoDialog : RewardsIntent()
    data object DismissCheckInInfoDialog : RewardsIntent()
}
